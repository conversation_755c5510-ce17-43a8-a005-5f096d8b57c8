import { Injectable, Logger } from '@nestjs/common';

/**
 * Redis Publisher Service for real-time workflow updates
 * Publishes test và execution events để BE app có thể subscribe
 */
@Injectable()
export class RedisPublisherService {
    private readonly logger = new Logger(RedisPublisherService.name);
    
    // Mock Redis client - trong thực tế sẽ inject Redis client
    private redis: any = {
        publish: async (channel: string, message: string) => {
            this.logger.log(`Publishing to ${channel}: ${message}`);
            // Actual Redis publish implementation
        },
        setex: async (key: string, ttl: number, value: string) => {
            this.logger.log(`Setting ${key} with TTL ${ttl}: ${value}`);
            // Actual Redis setex implementation
        }
    };

    /**
     * Publish test started event
     */
    async publishTestStarted(testId: string, userId: number, data: any) {
        const channel = `workflow_test_progress:${userId}`;
        const message = JSON.stringify({
            type: 'test_started',
            testId,
            userId,
            timestamp: Date.now(),
            data
        });

        await this.redis.publish(channel, message);
        
        // Store test session info
        const testKey = `test_execution:${testId}`;
        await this.redis.setex(testKey, 3600, JSON.stringify({
            testId,
            userId,
            status: 'running',
            startTime: Date.now(),
            ...data
        }));
    }

    /**
     * Publish test progress update
     */
    async publishTestProgress(testId: string, userId: number, progress: {
        currentStep: number;
        totalSteps: number;
        percentage: number;
        description?: string;
        nodeId?: string;
        nodeResult?: any;
    }) {
        const channel = `workflow_test_progress:${userId}`;
        const message = JSON.stringify({
            type: 'test_progress',
            testId,
            userId,
            timestamp: Date.now(),
            progress
        });

        await this.redis.publish(channel, message);

        // Update test progress in Redis
        const progressKey = `test_execution:${testId}:progress`;
        await this.redis.setex(progressKey, 3600, JSON.stringify(progress));

        // Store node result if provided
        if (progress.nodeId && progress.nodeResult) {
            const nodeKey = `test_execution:${testId}:nodes:${progress.nodeId}`;
            await this.redis.setex(nodeKey, 3600, JSON.stringify({
                nodeId: progress.nodeId,
                result: progress.nodeResult,
                timestamp: Date.now()
            }));
        }
    }

    /**
     * Publish test completed event
     */
    async publishTestCompleted(testId: string, userId: number, result: {
        success: boolean;
        output?: any;
        error?: any;
        executionTime: number;
        nodesExecuted: number;
        totalNodes: number;
    }) {
        const channel = `workflow_test_progress:${userId}`;
        const message = JSON.stringify({
            type: 'test_completed',
            testId,
            userId,
            timestamp: Date.now(),
            result
        });

        await this.redis.publish(channel, message);

        // Store final test result
        const resultKey = `test_execution:${testId}:result`;
        await this.redis.setex(resultKey, 3600, JSON.stringify({
            ...result,
            completedAt: Date.now()
        }));

        // Update test status
        const testKey = `test_execution:${testId}`;
        const testData = JSON.stringify({
            testId,
            userId,
            status: result.success ? 'completed' : 'failed',
            startTime: Date.now() - result.executionTime,
            endTime: Date.now(),
            result
        });
        await this.redis.setex(testKey, 3600, testData);
    }

    /**
     * Publish execution started event (for execute mode)
     */
    async publishExecutionStarted(executionId: string, userId: number, data: any) {
        const channel = `workflow_execution_status:${userId}`;
        const message = JSON.stringify({
            type: 'execution_started',
            executionId,
            userId,
            timestamp: Date.now(),
            data
        });

        await this.redis.publish(channel, message);
    }

    /**
     * Publish execution progress (for execute mode)
     */
    async publishExecutionProgress(executionId: string, userId: number, progress: any) {
        const channel = `workflow_execution_status:${userId}`;
        const message = JSON.stringify({
            type: 'execution_progress',
            executionId,
            userId,
            timestamp: Date.now(),
            progress
        });

        await this.redis.publish(channel, message);

        // Cache execution progress
        const progressKey = `execution:${executionId}:progress`;
        await this.redis.setex(progressKey, 86400, JSON.stringify(progress)); // 24h TTL
    }

    /**
     * Publish execution completed event (for execute mode)
     */
    async publishExecutionCompleted(executionId: string, userId: number, result: any) {
        const channel = `workflow_execution_status:${userId}`;
        const message = JSON.stringify({
            type: 'execution_completed',
            executionId,
            userId,
            timestamp: Date.now(),
            result
        });

        await this.redis.publish(channel, message);
    }

    /**
     * Publish error event
     */
    async publishError(id: string, userId: number, error: any, type: 'test' | 'execution') {
        const channel = type === 'test' 
            ? `workflow_test_progress:${userId}`
            : `workflow_execution_status:${userId}`;
            
        const message = JSON.stringify({
            type: `${type}_error`,
            [type === 'test' ? 'testId' : 'executionId']: id,
            userId,
            timestamp: Date.now(),
            error: {
                message: error.message,
                code: error.code || 'UNKNOWN_ERROR',
                stack: error.stack
            }
        });

        await this.redis.publish(channel, message);
    }

    /**
     * Get test result from Redis
     */
    async getTestResult(testId: string): Promise<any> {
        const resultKey = `test_execution:${testId}:result`;
        // Mock implementation - actual would use redis.get()
        return null;
    }

    /**
     * Get execution progress from Redis
     */
    async getExecutionProgress(executionId: string): Promise<any> {
        const progressKey = `execution:${executionId}:progress`;
        // Mock implementation - actual would use redis.get()
        return null;
    }

    /**
     * Cleanup test data (called after TTL expires or manual cleanup)
     */
    async cleanupTestData(testId: string) {
        const keys = [
            `test_execution:${testId}`,
            `test_execution:${testId}:progress`,
            `test_execution:${testId}:result`,
            `test_execution:${testId}:nodes:*`
        ];

        // Mock implementation - actual would use redis.del()
        this.logger.log(`Cleaning up test data for ${testId}`);
    }
}
